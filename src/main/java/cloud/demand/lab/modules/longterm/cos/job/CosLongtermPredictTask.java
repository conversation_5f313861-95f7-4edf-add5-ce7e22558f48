package cloud.demand.lab.modules.longterm.cos.job;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.enums.Constants;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisMsg;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 负责COS中长期模型预测的任务
 */
@Slf4j
@Service
public class CosLongtermPredictTask {

    @Resource
    RedisHelper redisHelper;
    @Resource
    private DBHelper cdLabDbHelper;

    /**
     * 处理来自消息队列中的任务。这个可以保证任务实时被处理。
     * 为什么用定时任务，因为生产的任务节点和web节点分来，确保任务执行异常不影响web节点。
     * 说明，这里会长期占用schedule池中的一条线程，schedule的线程数已由20调整为30，故没有影响。
     */
    @Scheduled(fixedDelay = 1L)
    public void fromRedisMq() {
        RedisMsg msg = redisHelper.receive(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS);
        if (msg != null) {
            Long taskId = NumberUtils.parseLong(msg.getMsg());
            try {
                if (taskId != null) {
                    CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
                    if (taskDO == null) {
                        log.error("cos task id {} not exist, ignore task", taskId);
                        return;
                    }
                    doRunTask(taskId);
                }
            } catch (Exception e) {
                log.error("run cos longterm predict task fail, taskId:{}", taskId, e);
            } finally {
                redisHelper.ack(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK_COS, msg.getUuid()); // 只处理一次
            }
        }
    }

    /**
     * 定期从数据库里扫描进行处理
     */
    @Scheduled(fixedDelay = 30000)
    public void fromDB() {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_status = ?", LongtermPredictTaskStatusEnum.NEW.getCode());

        List<CosLongtermPredictTaskDO> toRunTask = cdLabDbHelper.getAll(CosLongtermPredictTaskDO.class,
                whereSQL.getSQL(), whereSQL.getParams());
        for (CosLongtermPredictTaskDO task : toRunTask) {
            try {
                doRunTask(task.getId());
            } catch (Exception e) {
                log.error("run cos longterm predict task fail, taskId:{}", task.getId(), e); // 不影响其他任务执行
            }
        }
    }

    /**
     * 执行COS预测任务
     */
    @Synchronized(keyScript = "args[0]", waitLockMillisecond = 100)
    @Transactional(value = "cdlabTransactionManager")
    public void doRunTask(Long taskId) {

    }

    /**
     * 获取任务并设置状态为运行中
     */
    private CosLongtermPredictTaskDO getTaskAndSetStatus(Long taskId) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task == null) {
            return null;
        }

        // 只有状态为NEW的任务才能执行
        if (!LongtermPredictTaskStatusEnum.NEW.getCode().equals(task.getTaskStatus())) {
            log.warn("cos task {} status is {}, skip execution", taskId, task.getTaskStatus());
            return null;
        }

        // 设置任务状态为运行中
        task.setTaskStatus(LongtermPredictTaskStatusEnum.RUNNING.getCode());
        task.setTaskStartTime(LocalDateTime.now());
        task.setErrMsg(null);
        cdLabDbHelper.update(task);

        return task;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, LongtermPredictTaskStatusEnum status, String errMsg) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(status.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            if (errMsg != null && errMsg.length() > 500) {
                errMsg = errMsg.substring(0, 500); // 限制错误信息长度
            }
            task.setErrMsg(errMsg);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 更新任务状态为成功
     */
    private void updateTaskStatusSuccess(Long taskId, int costMs) {
        CosLongtermPredictTaskDO task = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(LongtermPredictTaskStatusEnum.SUCCESS.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            task.setCostMs(costMs);
            task.setErrMsg(null);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 计算预测结果
     * 根据用户填写的增速参数在存量基础上直接乘以增速算出未来关键日期的存量值
     */
    private List<CosLongtermPredictOutScaleDO> calculatePredictResults(
            CosLongtermPredictTaskDO task,
            List<CosLongtermPredictInputArgsDO> inputArgs,
            List<CosLongtermPredictInputScaleDO> scaleData) {

        List<CosLongtermPredictOutScaleDO> results = ListUtils.newArrayList();

        // 按内外部分组存量数据
        Map<Boolean, List<CosLongtermPredictInputScaleDO>> scaleDataByCustomerType =
                scaleData.stream().collect(Collectors.groupingBy(CosLongtermPredictInputScaleDO::getIsOutCustomer));

        // 获取最新的存量数据作为基准
        Map<Boolean, BigDecimal> latestScaleMap = getLatestScaleMap(scaleDataByCustomerType);

        // 对每个输入参数进行预测计算
        for (CosLongtermPredictInputArgsDO inputArg : inputArgs) {
            // 计算内部客户的预测结果
            if (inputArg.getScaleGrowthRateIn() != null) {
                BigDecimal baseScale = latestScaleMap.get(false); // false表示内部客户
                if (baseScale != null && baseScale.compareTo(BigDecimal.ZERO) > 0) {
                    List<CosLongtermPredictOutScaleDO> innerResults = calculatePredictForDateRange(
                            task.getId(), inputArg, baseScale, false);
                    results.addAll(innerResults);
                }
            }

            // 计算外部客户的预测结果
            if (inputArg.getScaleGrowthRateOut() != null) {
                BigDecimal baseScale = latestScaleMap.get(true); // true表示外部客户
                if (baseScale != null && baseScale.compareTo(BigDecimal.ZERO) > 0) {
                    List<CosLongtermPredictOutScaleDO> outerResults = calculatePredictForDateRange(
                            task.getId(), inputArg, baseScale, true);
                    results.addAll(outerResults);
                }
            }
        }

        return results;
    }

    /**
     * 获取最新的存量数据作为基准
     */
    private Map<Boolean, BigDecimal> getLatestScaleMap(Map<Boolean, List<CosLongtermPredictInputScaleDO>> scaleDataByCustomerType) {
        Map<Boolean, BigDecimal> latestScaleMap = new HashMap<>();

        for (Map.Entry<Boolean, List<CosLongtermPredictInputScaleDO>> entry : scaleDataByCustomerType.entrySet()) {
            Boolean isOutCustomer = entry.getKey();
            List<CosLongtermPredictInputScaleDO> scaleList = entry.getValue();

            if (!ListUtils.isEmpty(scaleList)) {
                // 按日期排序，取最新的数据
                ListUtils.sortDescNullLast(scaleList, CosLongtermPredictInputScaleDO::getDate);
                CosLongtermPredictInputScaleDO latestScale = scaleList.get(0);
                latestScaleMap.put(isOutCustomer, latestScale.getCurScale());
            }
        }

        return latestScaleMap;
    }

    /**
     * 计算指定日期范围内的预测结果
     */
    private List<CosLongtermPredictOutScaleDO> calculatePredictForDateRange(
            Long taskId,
            CosLongtermPredictInputArgsDO inputArg,
            BigDecimal baseScale,
            Boolean isOutCustomer) {

        List<CosLongtermPredictOutScaleDO> results = ListUtils.newArrayList();

        // 获取增速
        BigDecimal growthRate = isOutCustomer ? inputArg.getScaleGrowthRateOut() : inputArg.getScaleGrowthRateIn();
        if (growthRate == null) {
            return results;
        }

        // 计算时间范围内的关键日期
        LocalDate startDate = inputArg.getStartDate();
        LocalDate endDate = inputArg.getEndDate();

        if (startDate == null || endDate == null) {
            return results;
        }

        // 只在关键日期（月末）生成预测数据，不需要每一天都存
        LocalDate currentDate = startDate.withDayOfMonth(startDate.lengthOfMonth()); // 当月月末

        while (!currentDate.isAfter(endDate)) {
            // 计算从基准日期到当前日期的时间差（以月为单位）
            long monthsBetween = java.time.temporal.ChronoUnit.MONTHS.between(startDate, currentDate);

            // 根据增速计算预测值：基准存量 * (1 + 增速)^月数
            BigDecimal growthFactor = BigDecimal.ONE.add(growthRate);
            BigDecimal predictScale = baseScale;

            // 使用循环计算幂次方
            for (int i = 0; i < monthsBetween; i++) {
                predictScale = predictScale.multiply(growthFactor);
            }

            // 保留合适的精度
            predictScale = predictScale.setScale(4, RoundingMode.HALF_UP);

            // 创建预测结果记录
            CosLongtermPredictOutScaleDO result = new CosLongtermPredictOutScaleDO();
            result.setTaskId(taskId);
            result.setStrategyType(inputArg.getStrategyType());
            result.setDate(currentDate);
            result.setIsOutCustomer(isOutCustomer);
            result.setPredictScale(predictScale);

            results.add(result);

            // 移动到下个月末
            currentDate = currentDate.plusMonths(1).withDayOfMonth(currentDate.plusMonths(1).lengthOfMonth());
        }

        return results;
    }
}
